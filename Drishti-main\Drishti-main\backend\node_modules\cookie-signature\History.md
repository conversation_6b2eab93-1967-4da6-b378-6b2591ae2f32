1.2.2 / 2024-10-29
==================

* various metadata/documentation tweaks (incl. #51)


1.2.1 / 2023-02-27
==================

* update annotations for allowed secret key types (#44, thanks @jyasskin!)


1.2.0 / 2022-02-17
==================

* allow buffer and other node-supported types as key (#33)
* be pickier about extra content after signed portion (#40)
* some internal code clarity/cleanup improvements (#26)


1.1.0 / 2018-01-18
==================

* switch to built-in `crypto.timingSafeEqual` for validation instead of previous double-hash method (thank you @jodevsa!)


1.0.7 / 2023-04-12
==================

Later release for older node.js versions. See the [v1.0.x branch notes](https://github.com/tj/node-cookie-signature/blob/v1.0.x/History.md#107--2023-04-12).


1.0.6 / 2015-02-03
==================

* use `npm test` instead of `make test` to run tests
* clearer assertion messages when checking input


1.0.5 / 2014-09-05
==================

* add license to package.json

1.0.4 / 2014-06-25
==================

 * corrected avoidance of timing attacks (thanks @tenbits!)

1.0.3 / 2014-01-28
==================

 * [incorrect] fix for timing attacks

1.0.2 / 2014-01-28
==================

 * fix missing repository warning
 * fix typo in test

1.0.1 / 2013-04-15
==================

  * Revert "Changed underlying HMAC algo. to sha512."
  * Revert "Fix for timing attacks on MAC verification."

0.0.1 / 2010-01-03
==================

  * Initial release
