const express = require('express');
const db = require('../db');

const router = express.Router();

// List claims (minimal placeholder using DB if available)
router.get('/', (req, res) => {
  try {
    const rows = db.prepare('SELECT * FROM claims').all();
    res.json(rows);
  } catch (e) {
    // If table not found or any error, return empty list to keep server functional
    res.json([]);
  }
});

module.exports = router;

