Towards Deployable OCR Models for Indic
Languages

Minesh Mathew!0000—0002—0809—2590] | <PERSON><PERSON>]0000—0002—4808— 8860] and C
Vv Jawaharl0000—0001—6767—7057]

CVIT, International Institute of Information Technology, Hyderabad, India
<EMAIL>
{ajoy.mondal, jawahar}@iiit.ac.in

Abstract. The difficulty of reliably extracting characters had delayed
the character recognition solutions (or OCRs) in Indian languages. Con-
temporary rescarch in Indian language text recognition has shifted to-
wards recognizing text in word or line images without requiring sub-word
segmentation, leveraging Connectionist Temporal Classification (CTC)
for modeling unsegmented sequences. The next challenge is the lack of
public data for all these languages. And there is an immediate need to
lower the entry barricr for startups or solution providers. With this in
mind, (i) we introduce Mozhi datasct, a novel public datasct compris-
ing over 1.2 million annotated word images (cquivalent to approximately
120 thousand text linc images) across 13 languages. (ii) We conduct a
comprchensive empirical analysis of various neural network modcls em-
ploying CTC across 13 Indian languages. (iii) We also provide APIs for
our OCR models and web-based applications that integrate these APIs
to digitize Indic printed documents. We compare our modcl’s perfor-
mance with popular publicly available OCR tools for end-to-end docu-
ment image recognition. Our model outperform these OCR engines on 8
out of 13 languages. The code, trained models, and datasct are available
at https: //cvit.iiit.ac.in/usodi/tdocrmil. php.

Keywords: Printed text - Indic OCR - Indian languages - CRNN - CTC
- text recognition - APIs - web-based application.

1 Introduction

Text recognition faces challenges related to language/script, text rendering, and
imaging methods. This study concentrates on recognizing printed text in Indian
languages, particularly on text recognition alone, assuming cropped word or line
images are provided. The 2011 official census of India [1] lists 30 Indian lan-
guages with over a million native speakers, 22 of which are recognized as official
languages. These languages belong to three language families: Indo-European,
Dravidian, and Sino- Tibetan. Our focus is on text recognition in 13 official lan-
guages: Assamese, Bengali, Gujarati, Hindi, Kannada, Malayalam, Manipuri,
Marathi, Oriya, Punjabi, Tamil, Telugu, and Urdu. While some share linguis-
tic similarities, their scripts are distinct, with Devanagari script used in Hindi
