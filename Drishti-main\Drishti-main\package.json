{"name": "new", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@reduxjs/toolkit": "^2.9.0", "axios": "^1.11.0", "framer-motion": "^12.23.12", "jspdf": "^3.0.2", "leaflet": "^1.9.4", "leaflet-draw": "^1.0.4", "lucide-react": "^0.543.0", "react": "^19.1.1", "react-csv": "^2.2.2", "react-datepicker": "^8.7.0", "react-dom": "^19.1.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.62.0", "react-i18next": "^15.7.3", "react-is": "^19.1.1", "react-leaflet": "^5.0.0", "react-leaflet-draw": "^0.20.6", "react-router-dom": "^7.8.2", "recharts": "^3.2.0"}, "devDependencies": {"@eslint/js": "^9.33.0", "@tailwindcss/postcss": "^4.1.13", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "autoprefixer": "^10.4.21", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "vite": "^7.1.2"}}