{"name": "media-typer", "description": "Simple RFC 6838 media type parser and formatter", "version": "1.1.0", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": "jshttp/media-typer", "devDependencies": {"eslint": "5.16.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.17.2", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-node": "8.0.1", "eslint-plugin-promise": "4.1.1", "eslint-plugin-standard": "4.0.0", "mocha": "6.1.4", "nyc": "14.0.0"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "engines": {"node": ">= 0.8"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}}